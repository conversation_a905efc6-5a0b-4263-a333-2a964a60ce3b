<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>poran extends MY_Controller
{

    public function harian()
    {

        $this->load->model('Laporan_model');
        $start_date = $this->input->get('start_date') ?? date('Y-m-d');
        $end_date   = $this->input->get('end_date') ?? date('Y-m-d');

        $this->data['laporan']    = $this->Laporan_model->get_laporan_harian($start_date, $end_date);
        $this->data['start_date'] = $start_date;
        $this->data['end_date']   = $end_date;

        $this->data['outlet'] = $this->db->select(' p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();

        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/harian');
    }

    public function bulanan()
    {
        $bulan = $this->input->get('bulan') ?? date('Y-m');

        $this->load->model('Laporan_model');
        $this->data['laporan'] = $this->Laporan_model->get_laporan_bulanan($bulan);
        $this->data['bulan']   = $bulan;
        $this->data['outlet']  = $this->db->select(' p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();
        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/bulanan');
    }

    public function hapus_trx_harian($id)
    {
        if ($this->session->userdata('role') === 'admin') {
            // Ambil filter tanggal dari GET
            $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : date('Y-m-d');
            $end_date   = $this->input->get('end_date') ? $this->input->get('end_date') : date('Y-m-d');

            // Proses penghapusan transaksi berdasarkan kode struk
            $this->db->where('kode_struk', trim($id));
            $this->db->update('struk_pembayaran', ['status' => 'delete']);

            // Set flashdata sukses
            $this->session->set_flashdata('success', 'Transaksi berhasil dihapus.');

            // Redirect kembali ke halaman laporan harian dengan parameter tanggal yang sama
            redirect('laporan/harian?start_date=' . $start_date . '&end_date=' . $end_date);
        } else {
            // Jika bukan admin, redirect dengan pesan error
            $this->session->set_flashdata('error', 'Akses ditolak. Hanya admin yang dapat menghapus transaksi.');
            redirect('laporan/harian');
        }
    }

    // Hapus Transaksi Playground Harian
    public function hapus_trx_playground_harian($id)
    {
        if ($this->session->userdata('role') === 'admin') {
            // Ambil filter tanggal dari GET
            $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : date('Y-m-d');
            $end_date   = $this->input->get('end_date') ? $this->input->get('end_date') : date('Y-m-d');

            // Proses penghapusan transaksi berdasarkan kode struk
            $this->db->where('kode_struk', trim($id));
            $this->db->update('struk_pembayaran', ['status' => 'delete']);

            // Set flashdata sukses
            $this->session->set_flashdata('success', 'Transaksi playground berhasil dihapus.');

            // Redirect kembali ke halaman laporan playground harian dengan parameter tanggal yang sama
            redirect('laporan/playground_harian?start_date=' . $start_date . '&end_date=' . $end_date);
        } else {
            // Jika bukan admin, redirect dengan pesan error
            $this->session->set_flashdata('error', 'Akses ditolak. Hanya admin yang dapat menghapus transaksi.');
            redirect('laporan/playground_harian');
        }
    }

    // Hapus Transaksi Glamping Harian
    public function hapus_trx_glamping_harian($id)
    {
        if ($this->session->userdata('role') === 'admin') {
            // Ambil filter tanggal dari GET
            $dari_tanggal = $this->input->get('dari_tanggal') ? $this->input->get('dari_tanggal') : date('Y-m-d');
            $sampai_tanggal = $this->input->get('sampai_tanggal') ? $this->input->get('sampai_tanggal') : date('Y-m-d');

            // Proses penghapusan transaksi berdasarkan kode struk
            $this->db->where('kode_struk', trim($id));
            $this->db->update('struk_pembayaran', ['status' => 'delete']);

            // Set flashdata sukses
            $this->session->set_flashdata('success', 'Transaksi glamping berhasil dihapus.');

            // Redirect kembali ke halaman laporan glamping harian dengan parameter tanggal yang sama
            redirect('laporan/glamping_harian?dari_tanggal=' . $dari_tanggal . '&sampai_tanggal=' . $sampai_tanggal);
        } else {
            // Jika bukan admin, redirect dengan pesan error
            $this->session->set_flashdata('error', 'Akses ditolak. Hanya admin yang dapat menghapus transaksi.');
            redirect('laporan/glamping_harian');
        }
    }

    // Laporan Playground - Harian
    public function playground_harian()
    {
        $this->load->model('Laporan_model');
        $start_date = $this->input->get('start_date') ?? date('Y-m-d');
        $end_date   = $this->input->get('end_date') ?? date('Y-m-d');

        $this->data['laporan']    = $this->Laporan_model->get_laporan_playground_harian($start_date, $end_date);
        $this->data['start_date'] = $start_date;
        $this->data['end_date']   = $end_date;
        $this->data['jenis']      = 'playground';

        $this->data['outlet'] = $this->db->select('p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();

        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/playground_harian');
    }

    // Laporan Playground - Bulanan
    public function playground_bulanan()
    {
        $bulan = $this->input->get('bulan') ?? date('Y-m');

        $this->load->model('Laporan_model');
        $this->data['laporan'] = $this->Laporan_model->get_laporan_playground_bulanan($bulan);
        $this->data['bulan']   = $bulan;
        $this->data['jenis']   = 'playground';
        
        $this->data['outlet']  = $this->db->select('p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();
            
        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/playground_bulanan');
    }

    // Laporan Glamping - Harian
    public function glamping_harian()
    {
        $this->load->model('Laporan_model');

        // Ambil parameter dari GET request dengan nama yang sesuai dengan form
        $dari_tanggal = $this->input->get('dari_tanggal') ?? date('Y-m-d');
        $sampai_tanggal = $this->input->get('sampai_tanggal') ?? date('Y-m-d');

        // Ambil data laporan
        $laporan_data = $this->Laporan_model->get_laporan_glamping_harian($dari_tanggal, $sampai_tanggal);

        // Hitung total untuk footer tabel dan ambil detail item
        $total_qty = 0;
        $total_glamping = 0;

        foreach ($laporan_data as $item) {
            $total_qty += $item->jml_item;
            $total_glamping += $item->total_glamping;

            // Ambil detail item untuk setiap transaksi
            $detail_items = $this->db->where('kode_struk', $item->kode_struk)
                                   ->where('jenis_item', 'glamping')
                                   ->get('struk_pembayaran_detail')
                                   ->result();

            $item_names = [];
            foreach($detail_items as $detail) {
                $item_names[] = $detail->nama_item . ' (' . $detail->jumlah . 'x)';
            }
            $item->detail_items = implode(', ', $item_names);
        }

        // Kirim data ke view
        $this->data['laporan'] = $laporan_data;
        $this->data['dari_tanggal'] = $dari_tanggal;
        $this->data['sampai_tanggal'] = $sampai_tanggal;
        $this->data['total_qty'] = $total_qty;
        $this->data['total_glamping'] = $total_glamping;
        $this->data['jenis'] = 'glamping';

        $this->data['outlet'] = $this->db->select('p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();

        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/glamping_harian');
    }

    // Laporan Glamping - Bulanan
    public function glamping_bulanan()
    {
        $bulan = $this->input->get('bulan') ?? date('Y-m');

        $this->load->model('Laporan_model');
        $this->data['laporan'] = $this->Laporan_model->get_laporan_glamping_bulanan($bulan);
        $this->data['bulan']   = $bulan;
        $this->data['jenis']   = 'glamping';
        
        $this->data['outlet']  = $this->db->select('p.outlet_name, p.address, p.phone')
            ->from('profile_outlet p')
            ->get()
            ->row_array();
            
        $this->data['output_footer'] = 'templates/footer_tabel';
        $this->render_view('laporan/glamping_bulanan');
    }

}
