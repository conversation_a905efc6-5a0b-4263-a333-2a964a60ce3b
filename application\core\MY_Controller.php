<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Controller extends CI_Controller {
    protected $data = array();
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        $this->load->library(['grocery_CRUD', 'session']);
        
        // Check login
        if (!$this->session->userdata('logged_in_loker_renang')) {
            redirect('auth/login');
        }
        
        // Basic template data
        $this->data['page_title'] = 'Kolam Renang Management System';
       // $this->data['user'] = $this->session->userdata();
    }
    
    protected function render_view($view, $data = []) {
        $this->load->view('templates/header', $this->data);
        $this->load->view('templates/sidebar', $this->data);
        $this->load->view($view, $data);
        $this->load->view('templates/footer');
    }
    
    protected function render_crud($output = null,$output_footer =null) {
        $this->data['output'] = $output;
          $this->data['output_footer'] = $output_footer;
        $this->render_view('templates/grocery_crud_view');
    }
}