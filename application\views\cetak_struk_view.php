<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Struk <?php echo $kode_struk; ?></title>
    <script src="<?php echo base_url(); ?>printer_assets/jquery.min.js"></script>
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/font-awesome.min.css">
    <script src="<?php echo base_url(); ?>printer_assets/bootstrap.min.js"></script>
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/size_56mm.css" media="all">
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/print_bill.css" media="all">

    <!-- QR Code Library - davidshimjs-qrcodejs -->
    <script src="https://cdn.jsdelivr.net/npm/davidshimjs-qrcodejs@0.0.2/qrcode.min.js"></script>

    <style>
        /* QR Code Styling */
        #qrcode {
            margin: 10px auto;
            padding: 5px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        #qrcode canvas {
            display: block;
            margin: 0 auto;
        }

        /* Hide QR code when printing if needed */
        @media print {
            #qrcode {
                /* Uncomment next line if you want to hide QR code in print */
                /* display: none; */
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <div id="receiptData">
            <div id="receipt-data">
                <div class="text-center">
                    <h3><?php echo $outlet_name; ?></h3>
                    <p>
                        <?php echo $address; ?><br>
                        Telp: <?php echo $phone; ?><br>
                        No. Struk: <?php echo $kode_struk; ?><br>
                    </p>
                </div>

                <p>
                    Tanggal: <?php echo date('d-m-Y H:i', strtotime($tanggal)); ?><br>
                    Kasir:  <b><?php echo $kasir; ?></b> <br>
                    <?php if(!empty($no_hp)): ?>
                    No. HP: <?php echo $no_hp; ?><br>
                    <?php endif; ?>
                </p>

                <div class="ir_clear"></div>
                <table class="table table-condensed">
                    <tbody>
                        <?php
                        $total_items = 0;
                        $no = 0;
                        foreach ($items as $item): 
                            $no++;
                            $total_items += $item['jumlah'];
                        ?>
                        <tr>
                            <td class="no-border border-bottom" style="width: 70%">
                                <?php echo '# '.$no.': '.$item['nama_item']; ?>
                                <small></small> 
                                <?php echo $item['jumlah'].' X '.number_format($item['harga_satuan'], 0, ',', '.'); ?>
                            </td>
                            <td class="no-border border-bottom text-right">
                                Rp. <?php echo number_format($item['subtotal'], 0, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>

                        <?php if (!empty($lokers)): ?>
                        <tr>
                            <td class="no-border border-bottom" colspan="2">
                                <strong>Sewa Loker:</strong> 
                                <?php
                                $loker_numbers = array_map(function($loker) {
                                    return 'No. ' . $loker['nomor_loker'];
                                }, $lokers);
                                echo implode(', ', $loker_numbers);
                                ?>
                                <br>
                                <!-- <small>Harga sewa: Rp <?php echo number_format($lokers[0]['harga_sewa'], 0, ',', '.'); ?> per unit</small> -->
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total Item: <?php echo $total_items; ?></th>
                            <th class="ir_txt_left"></th>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <th class="text-right">
                                Rp <?php echo number_format($total_harga, 0, ',', '.'); ?>
                            </th>
                        </tr>
                        <tr>
                            <th><?=ucfirst($metode_bayar);?></th>
                            <th class="text-right">
                                Rp <?php echo number_format($jumlah_bayar, 0, ',', '.'); ?>
                            </th>
                        </tr>
                        <tr>
                            <th>Kembali</th>
                            <th class="text-right">
                                Rp <?php echo number_format($kembalian, 0, ',', '.'); ?>
                            </th>
                        </tr>
                    </tfoot>
                </table>

                <p class="text-center">
                    === Terima Kasih ===<br>
                    Simpan struk ini sebagai bukti pembayaran
                    <?php if (!empty($lokers)): ?>
                    <br>
                    <small>* Kunci loker harus dikembalikan setelah selesai (kunci loker hilang denda Rp 35.000) </small>
                    <?php endif; ?>
                </p>

                <!-- QR Code untuk WhatsApp -->
                <div class="text-center" style="margin-top: 15px;">
                     <small style="font-size: 10px;">Scan QR for Info</small>
                    <br> 
                    <div id="qrcode" style="display: inline-block; min-height: 80px; min-width: 80px;">
                        <small style="color: #999;">Loading QR Code...</small>
                    </div>
                   
                   
                    <!-- <br>
                    <small style="font-size: 8px; color: #666;">WA: <?php echo $phone; ?></small> -->
                </div>
            </div>
            <div class="ir_clear"></div>
        </div>

        <div id="buttons"  class="no-print ir_pt_tr">
            <hr>
            <span class="pull-right col-xs-12">
                <button onclick="window.print();window.close();" class="btn btn-block btn-primary">Cetak</button>
                 <!-- <button onclick="" class="btn btn-block btn-danger">Cancel Transaksi</button> -->
            </span>
            <div class="ir_clear"></div>
            <div class="col-xs-12" style="background-color: #f5f5f5; padding: 10px; margin-top: 10px;">
                <p style="font-size: 12px;">
                    Petunjuk sebelum mencetak:<br>
                    1. Nonaktifkan Header dan Footer di pengaturan cetak browser<br>
                    - Firefox: File > Page Setup > Margins & Header/Footer > Headers & Footers > Set semua --blank--<br>
                    - Chrome: Menu > Print > Uncheck Header/Footer di More Options
                </p>
            </div>

        </div>

    </div>

    <script>
        // Generate QR Code untuk WhatsApp menggunakan davidshimjs-qrcodejs
        function generateWhatsAppQR() {
            // Ambil nomor telepon dari PHP dan format untuk WhatsApp
            var phoneNumber = '<?php echo $phone; ?>';

            // Bersihkan nomor telepon dari karakter non-digit
            var cleanPhone = phoneNumber.replace(/\D/g, '');

            // Format nomor untuk WhatsApp (tambahkan 62 jika dimulai dengan 0)
            if (cleanPhone.startsWith('0')) {
                cleanPhone = '62' + cleanPhone.substring(1);
            } else if (!cleanPhone.startsWith('62')) {
                cleanPhone = '62' + cleanPhone;
            }

            // Buat URL WhatsApp dengan pesan default
            var defaultMessage = '';
            var whatsappUrl = 'https://wa.me/' + cleanPhone + '?text=' + encodeURIComponent(defaultMessage);

            console.log('Phone Number:', phoneNumber);
            console.log('Clean Phone:', cleanPhone);
            console.log('WhatsApp URL:', whatsappUrl);
            console.log('QRCode available:', typeof QRCode !== 'undefined');

            // Generate QR Code menggunakan davidshimjs-qrcodejs
            var qrCodeElement = document.getElementById('qrcode');
            if (!qrCodeElement) {
                console.error('QR code element not found');
                return;
            }

            // Cek apakah library QRCode tersedia
            if (typeof QRCode !== 'undefined') {
                try {
                    // Clear loading text
                    qrCodeElement.innerHTML = '';

                    // Generate QR Code dengan davidshimjs-qrcodejs
                    var qrcode = new QRCode(qrCodeElement, {
                        text: whatsappUrl,
                        width: 100,
                        height: 80,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.M
                    });

                    console.log('QR Code generated successfully with davidshimjs-qrcodejs');

                } catch (e) {
                    console.error('Exception in QR generation:', e);
                    // Fallback ke API online
                    generateFallbackQR(whatsappUrl, qrCodeElement);
                }
            } else {
                console.warn('QRCode library not loaded, using fallback');
                generateFallbackQR(whatsappUrl, qrCodeElement);
            }
        }

        // Fallback method menggunakan API online
        function generateFallbackQR(whatsappUrl, qrCodeElement) {
            try {
                var qrApiUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=80x80&format=png&margin=5&data=' + encodeURIComponent(whatsappUrl);

                // Test apakah image bisa di-load
                var testImg = new Image();
                testImg.onload = function() {
                    qrCodeElement.innerHTML = '<img src="' + qrApiUrl + '" alt="QR Code WhatsApp" style="width: 80px; height: 80px; border: 1px solid #ddd; border-radius: 5px;">';
                    console.log('QR Code generated successfully via fallback API');
                };
                testImg.onerror = function() {
                    console.error('Fallback API also failed');
                    qrCodeElement.innerHTML = '<div style="width: 80px; height: 80px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; font-size: 10px; text-align: center; background: #f8f9fa;">QR Error</div>';
                };
                testImg.src = qrApiUrl;

            } catch (e) {
                console.error('Error with fallback QR API:', e);
                qrCodeElement.innerHTML = '<div style="width: 80px; height: 80px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; font-size: 10px; text-align: center; background: #f8f9fa;">QR Failed</div>';
            }
        }

        // Tunggu sampai DOM dan library ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, generating QR code...');
            // Delay untuk memastikan library ter-load
            setTimeout(generateWhatsAppQR, 500);
        });

        // Fallback untuk browser yang sudah loaded
        if (document.readyState !== 'loading') {
            console.log('DOM already loaded, generating QR code...');
            setTimeout(generateWhatsAppQR, 500);
        }
    </script>

</body>
</html>