<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Struk <?php echo $kode_struk; ?></title>
    <script src="<?php echo base_url(); ?>printer_assets/jquery.min.js"></script>
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/font-awesome.min.css">
    <script src="<?php echo base_url(); ?>printer_assets/bootstrap.min.js"></script>
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/size_56mm.css" media="all">
    <link rel="stylesheet" href="<?php echo base_url(); ?>printer_assets/print_bill.css" media="all">

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <div id="wrapper">
        <div id="receiptData">
            <div id="receipt-data">
                <div class="text-center">
                    <h3><?php echo $outlet_name; ?></h3>
                    <p>
                        <?php echo $address; ?><br>
                        Telp: <?php echo $phone; ?><br>
                        No. Struk: <?php echo $kode_struk; ?><br>
                    </p>
                </div>

                <p>
                    Tanggal: <?php echo date('d-m-Y H:i', strtotime($tanggal)); ?><br>
                    Kasir:  <b><?php echo $kasir; ?></b> <br>
                    <?php if(!empty($no_hp)): ?>
                    No. HP: <?php echo $no_hp; ?><br>
                    <?php endif; ?>
                </p>

                <div class="ir_clear"></div>
                <table class="table table-condensed">
                    <tbody>
                        <?php
                        $total_items = 0;
                        $no = 0;
                        foreach ($items as $item): 
                            $no++;
                            $total_items += $item['jumlah'];
                        ?>
                        <tr>
                            <td class="no-border border-bottom" style="width: 70%">
                                <?php echo '# '.$no.': '.$item['nama_item']; ?>
                                <small></small> 
                                <?php echo $item['jumlah'].' X '.number_format($item['harga_satuan'], 0, ',', '.'); ?>
                            </td>
                            <td class="no-border border-bottom text-right">
                                Rp. <?php echo number_format($item['subtotal'], 0, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>

                        <?php if (!empty($lokers)): ?>
                        <tr>
                            <td class="no-border border-bottom" colspan="2">
                                <strong>Sewa Loker:</strong> 
                                <?php
                                $loker_numbers = array_map(function($loker) {
                                    return 'No. ' . $loker['nomor_loker'];
                                }, $lokers);
                                echo implode(', ', $loker_numbers);
                                ?>
                                <br>
                                <!-- <small>Harga sewa: Rp <?php echo number_format($lokers[0]['harga_sewa'], 0, ',', '.'); ?> per unit</small> -->
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total Item: <?php echo $total_items; ?></th>
                            <th class="ir_txt_left"></th>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <th class="text-right">
                                Rp <?php echo number_format($total_harga, 0, ',', '.'); ?>
                            </th>
                        </tr>
                        <tr>
                            <th><?=ucfirst($metode_bayar);?></th>
                            <th class="text-right">
                                Rp <?php echo number_format($jumlah_bayar, 0, ',', '.'); ?>
                            </th>
                        </tr>
                        <tr>
                            <th>Kembali</th>
                            <th class="text-right">
                                Rp <?php echo number_format($kembalian, 0, ',', '.'); ?>
                            </th>
                        </tr>
                    </tfoot>
                </table>

                <p class="text-center">
                    === Terima Kasih ===<br>
                    Simpan struk ini sebagai bukti pembayaran
                    <?php if (!empty($lokers)): ?>
                    <br>
                    <small>* Kunci loker harus dikembalikan setelah selesai (kunci loker hilang denda Rp 35.000) </small>
                    <?php endif; ?>
                </p>

                <!-- QR Code untuk WhatsApp -->
                <div class="text-center" style="margin-top: 15px;">
                    <div id="qrcode" style="display: inline-block;"></div>
                    <br>
                    <small style="font-size: 10px;">Scan QR untuk chat WhatsApp</small>
                </div>
            </div>
            <div class="ir_clear"></div>
        </div>

        <div id="buttons"  class="no-print ir_pt_tr">
            <hr>
            <span class="pull-right col-xs-12">
                <button onclick="window.print();window.close();" class="btn btn-block btn-primary">Cetak</button>
                 <!-- <button onclick="" class="btn btn-block btn-danger">Cancel Transaksi</button> -->
            </span>
            <div class="ir_clear"></div>
            <div class="col-xs-12" style="background-color: #f5f5f5; padding: 10px; margin-top: 10px;">
                <p style="font-size: 12px;">
                    Petunjuk sebelum mencetak:<br>
                    1. Nonaktifkan Header dan Footer di pengaturan cetak browser<br>
                    - Firefox: File > Page Setup > Margins & Header/Footer > Headers & Footers > Set semua --blank--<br>
                    - Chrome: Menu > Print > Uncheck Header/Footer di More Options
                </p>
            </div>

        </div>

    </div>

    <script>
        // Generate QR Code untuk WhatsApp
        document.addEventListener('DOMContentLoaded', function() {
            // Ambil nomor telepon dari PHP dan format untuk WhatsApp
            var phoneNumber = '<?php echo $phone; ?>';

            // Bersihkan nomor telepon dari karakter non-digit
            var cleanPhone = phoneNumber.replace(/\D/g, '');

            // Format nomor untuk WhatsApp (tambahkan 62 jika dimulai dengan 0)
            if (cleanPhone.startsWith('0')) {
                cleanPhone = '62' + cleanPhone.substring(1);
            } else if (!cleanPhone.startsWith('62')) {
                cleanPhone = '62' + cleanPhone;
            }

            // Buat URL WhatsApp dengan pesan default
            var defaultMessage = 'Halo, saya ingin bertanya tentang <?php echo $outlet_name; ?>';
            var whatsappUrl = 'https://wa.me/' + cleanPhone + '?text=' + encodeURIComponent(defaultMessage);

            // Generate QR Code
            var qrCodeElement = document.getElementById('qrcode');
            if (qrCodeElement && typeof QRCode !== 'undefined') {
                QRCode.toCanvas(qrCodeElement, whatsappUrl, {
                    width: 80,
                    height: 80,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error) {
                    if (error) {
                        console.error('Error generating QR code:', error);
                        // Fallback: tampilkan teks jika QR code gagal
                        qrCodeElement.innerHTML = '<small>QR Code Error</small>';
                    }
                });
            } else {
                // Fallback jika library tidak tersedia
                console.warn('QRCode library not loaded');
                if (qrCodeElement) {
                    qrCodeElement.innerHTML = '<small>QR Code tidak tersedia</small>';
                }
            }
        });
    </script>

</body>
</html>