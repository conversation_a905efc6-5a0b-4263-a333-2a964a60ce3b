<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/dataTables.bootstrap4.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/pnotify.custom.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/datatables.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/extras/TableTools/media/css/TableTools.css" />

<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Dashboard</h1> <?=$page_title;?>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- Small Box: Total Pendapatan -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>Rp <?= number_format($total_pendapatan, 0, ',', '.') ?></h3>
                            <p>Total Pendapatan</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>

           
                <!-- Small Box: Transaksi Hari Ini -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?= count($transaksi_hari_ini) ?></h3>
                            <p>Transaksi Hari Ini</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table: Transaksi Terbaru -->
            <div class="row">
                <div class="col-12">

                <div class="card">
    <div class="card-header">
        <h5 class="card-title">Pendapatan Bulanan Berdasarkan Metode Pembayaran (<?= date('Y') ?>)</h5>
    </div>
    <div class="card-body">
        <div id="chart-pendapatan-bulanan" style="height: 300px;"></div>
    </div>
</div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Transaksi Terbaru Hari Ini Tanggal : <?=date('Y-m-d');?> </h3>
                        </div>
                        <div class="card-body">

                        <!-- Di dalam view, tambahkan div untuk chart -->


                            <div class="table-responsive">
                            <table id="tabel" class="table table-bordered table-striped ">
                            <thead>
                                <tr> <th>No</th>
                                    <th>No Struk</th>
                                    <th>Waktu</th>
                                    <th>Jumlah Item</th>
                                    <th>Total Harga</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $no=1; foreach ($transaksi_hari_ini as $transaksi): ?>
                                <tr> <td><?= $no++; ?></td>
                                    <td><?= $transaksi->kode_struk ?></td>
                                    <td><?= date('H:i:s', strtotime($transaksi->created_at)) ?></td>
                                    <td><?= $transaksi->jml_item ?></td>
                                    <td>Rp <?= number_format($transaksi->total_harga, 0, ',', '.') ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- DataTables CSS -->




<!-- Pastikan sudah include library ApexCharts -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Data dari controller
    var chartData = <?= json_encode($pendapatan_bulanan) ?>;
    
    // Persiapkan data untuk bar chart total (menjumlahkan semua metode pembayaran)
    var totalData = Array(12).fill(0);
    var metodeDetails = {}; // Untuk menyimpan rincian tiap bulan
    
    // Inisialisasi array untuk detail tiap bulan
    for (var i = 0; i < chartData.bulan.length; i++) {
        metodeDetails[i] = [];
    }
    
    // Jumlahkan total dan simpan detail
    chartData.series.forEach(function(metode) {
        for (var i = 0; i < metode.data.length; i++) {
            totalData[i] += metode.data[i];
            if (metode.data[i] > 0) {
                metodeDetails[i].push({
                    metode: metode.name,
                    total: metode.data[i]
                });
            }
        }
    });
    
    var options = {
        series: [{
            name: 'Total Pendapatan',
            data: totalData
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: true
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '60%',
                endingShape: 'rounded',
                borderRadius: 4
            }
        },
        dataLabels: {
            enabled: false
        },
        colors: ['#2E93fA'],
        xaxis: {
            categories: chartData.bulan,
            title: {
                text: 'Bulan'
            }
        },
        yaxis: {
            title: {
                text: 'Total Pendapatan (Rp)'
            },
            labels: {
                formatter: function(val) {
                    return 'Rp ' + val.toLocaleString('id-ID', {
                        maximumFractionDigits: 0
                    });
                }
            }
        },
      
        title: {
            <?php if ($this->session->userdata('role') === 'admin'): ?>
            text: 'Total Pendapatan Bulanan Tahun ' + new Date().getFullYear(),
            <?php else: ?>
                text: 'Total Pendapatan Bulanan Tahun ' + new Date().getFullYear() + ' Oleh <?php echo $this->session->userdata('username');?>',
                <?php endif; ?>
            align: 'center'
        },
        tooltip: {
            enabled: true,
            shared: true,
            intersect: false,
            custom: function({ series, seriesIndex, dataPointIndex, w }) {
                var details = metodeDetails[dataPointIndex];
                var bulan = chartData.bulan[dataPointIndex];
                var total = series[seriesIndex][dataPointIndex];
                
                // Buat konten tooltip
                var content = '<div class="apexcharts-tooltip-title" style="font-weight:bold;margin-bottom:5px;text-align:center">' + bulan + ' ' + new Date().getFullYear() + '</div>';
                content += '<div style="padding: 5px;">';
                content += '<div style="font-weight:bold;margin-bottom:5px;border-bottom:1px solid #ddd;padding-bottom:3px">Total: Rp ' + total.toLocaleString('id-ID') + '</div>';
                
                // Tambahkan rincian per metode pembayaran
                content += '<div style="font-weight:bold;margin-bottom:2px">Rincian Metode Pembayaran:</div>';
                
                if (details.length > 0) {
                    details.forEach(function(detail) {
                        var percentage = ((detail.total / total) * 100).toFixed(1);
                        content += '<div style="display:flex;justify-content:space-between;margin-bottom:2px">';
                        content += '<span>' + detail.metode + ':</span>';
                        content += '<span>Rp ' + detail.total.toLocaleString('id-ID') + ' (' + percentage + '%)</span>';
                        content += '</div>';
                    });
                } else {
                    content += '<div>Tidak ada pendapatan</div>';
                }
                
                content += '</div>';
                return content;
            }
        }
    };

    var chart = new ApexCharts(document.querySelector("#chart-pendapatan-bulanan"), options);
    chart.render();
});
</script>