<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/dataTables.bootstrap4.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/pnotify.custom.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/datatables.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/extras/TableTools/media/css/TableTools.css" />

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><PERSON><PERSON><PERSON></h1>
                    <h2 id="outlet_name" style="display:none;"><?=$outlet['outlet_name'];?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Flashdata untuk pesan error/sukses -->
    <?php if($this->session->flashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $this->session->flashdata('error'); ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if($this->session->flashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <form class="form-inline" method="get">
                                        <div class="form-group mr-2">
                                            <label class="mr-2">Dari Tanggal: </label>
                                            <input type="date" name="dari_tanggal" class="form-control" value="<?= $dari_tanggal ?>">
                                        </div>
                                        <div class="form-group mr-2">
                                            <label class="mr-2">Sampai Tanggal: </label>
                                            <input type="date" name="sampai_tanggal" class="form-control" value="<?= $sampai_tanggal ?>">
                                        </div>
                                        <button type="submit" class="btn btn-success">Tampilkan</button>
                                    </form>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button onclick="printElement('printArea')" class="btn btn-success">
                                        <i class="fas fa-print"></i> Cetak
                                    </button> 
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive" id="printArea">
                                <h1 style="display: none">
                                    Laporan Glamping Harian
                                    <?php if($dari_tanggal == $sampai_tanggal): ?>
                                        Tanggal <?= date('d F Y', strtotime($dari_tanggal)); ?>
                                    <?php else: ?>
                                        Periode <?= date('d F Y', strtotime($dari_tanggal)); ?> - <?= date('d F Y', strtotime($sampai_tanggal)); ?>
                                    <?php endif; ?>
                                </h1>
                                <table class="table table-bordered table-striped" id="tabel">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Tanggal</th>
                                            <th>Kode Transaksi</th>
                                            <th>Nama Item</th>
                                            <th>Jumlah</th>
                                            <th>Harga Satuan</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <?php if ($this->session->userdata('role') == 'admin') { ?>
                                            <th>Aksi</th>
                                            <?php } ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        if (!empty($laporan)):
                                            foreach($laporan as $item):
                                        ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td><?= date('d/m/Y H:i', strtotime($item->tanggal)) ?></td>
                                            <td><?= $item->kode_struk ?></td>
                                            <td><?= $item->detail_items ?></td>
                                            <td><?= $item->jml_item ?></td>
                                            <td>-</td>
                                            <td>Rp <?= number_format($item->total_glamping, 0, ',', '.') ?></td>
                                            <td>
                                                <?php if($item->status == 'aktif'): ?>
                                                    <span class="badge badge-success">Aktif</span>
                                                <?php elseif($item->status == 'selesai'): ?>
                                                    <span class="badge badge-primary">Selesai</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Dibatalkan</span>
                                                <?php endif; ?>
                                            </td>
                                            <?php if ($this->session->userdata('role') == 'admin') { ?>
                                            <td>
                                                <button class="btn btn-primary btn-sm"
                                                    onclick="window.open('<?=base_url();?>/order/cetak/<?= $item->kode_struk ?>', 'Cetak Struk', 'location=no,width=450,height=650,scrollbars=yes,top=100,left=700,resizable=no');">
                                                    <i class="fas fa-eye"></i> Lihat Struk
                                                </button>
                                                <button class="btn btn-danger btn-sm"
                                                    onclick="confirmDeleteGlamping('<?=base_url();?>/laporan/hapus_trx_glamping_harian/<?= $item->kode_struk ?>?dari_tanggal=<?= $dari_tanggal ?>&sampai_tanggal=<?= $sampai_tanggal ?>', '<?= $item->kode_struk ?>');">
                                                    <i class="fas fa-trash"></i> Hapus
                                                </button>
                                            </td>
                                            <?php } ?>
                                        </tr>
                                        <?php
                                            endforeach;
                                        else:
                                        ?>
                                        <tr>
                                            <td colspan="<?= $this->session->userdata('role') == 'admin' ? '9' : '8' ?>" class="text-center">Tidak ada data untuk periode yang dipilih</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="4">Total</th>
                                            <th><?= $total_qty ?></th>
                                            <th>-</th>
                                            <th>Rp <?= number_format($total_glamping, 0, ',', '.') ?></th>
                                            <th>-</th>
                                            <?php if ($this->session->userdata('role') == 'admin') { ?>
                                            <th>-</th>
                                            <?php } ?>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="<?php echo base_url(); ?>printer_assets/jspdf.umd.min.js"></script>
<script src="<?php echo base_url(); ?>printer_assets/jspdf.plugin.autotable.min.js"></script>
<script>
function decodeHTMLEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

function printElement(elementId) {
    const printContent = document.getElementById(elementId).innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="padding: 20px;">
            <h1 style="text-align: center; display: block !important;">${document.querySelector('h1.m-0').textContent}</h1>
            <h2 style="text-align: center; display: block !important;">${document.getElementById('outlet_name').textContent}</h2>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    
    // Reattach event listeners after restoring content
    attachEventListeners();
}

function generateDailyPDF() {
    // Get date range and outlet name
    const fromDate = document.querySelector('input[name="dari_tanggal"]').value;
    const toDate = document.querySelector('input[name="sampai_tanggal"]').value;
    const outletName = decodeHTMLEntities(document.getElementById('outlet_name').textContent);
    
    // Format dates for display
    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
        });
    };

    // Initialize jsPDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Add main title
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.text('Laporan Glamping Harian', doc.internal.pageSize.width / 2, 20, { align: 'center' });

    // Add outlet name as subtitle
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text(outletName, doc.internal.pageSize.width / 2, 28, { align: 'center' });

    // Add date range
    doc.setFontSize(11);
    const dateRange = fromDate === toDate 
        ? `Tanggal: ${formatDate(fromDate)}`
        : `Periode: ${formatDate(fromDate)} - ${formatDate(toDate)}`;
    doc.text(dateRange, doc.internal.pageSize.width / 2, 36, { align: 'center' });

    // Get data from table
    const tableData = [];
    let totalQty = 0;
    let totalAmount = 0;
    
    document.querySelectorAll('table tbody tr').forEach(row => {
        const rowData = [
            row.cells[0].textContent, // No
            row.cells[1].textContent, // Tanggal
            row.cells[2].textContent, // Kode Transaksi
            decodeHTMLEntities(row.cells[3].textContent), // Nama Item
            row.cells[4].textContent, // Jumlah
            decodeHTMLEntities(row.cells[5].textContent), // Harga Satuan
            decodeHTMLEntities(row.cells[6].textContent), // Total
            decodeHTMLEntities(row.cells[7].textContent.replace(/<[^>]*>/g, '')) // Status (remove HTML tags)
        ];
        tableData.push(rowData);
        
        // Update totals
        totalQty += parseInt(row.cells[4].textContent);
        totalAmount += parseInt(row.cells[6].textContent.replace('Rp ', '').replace(/\./g, ''));
    });

    // Add table
    doc.autoTable({
        startY: 45,
        head: [['No', 'Tanggal', 'Kode', 'Nama Item', 'Qty', 'Harga', 'Total', 'Status']],
        body: tableData,
        foot: [['', '', '', 'Total', totalQty.toString(), '-', `Rp ${totalAmount.toLocaleString('id-ID')}`, '-']],
        theme: 'grid',
        headStyles: {
            fillColor: [40, 167, 69],
            textColor: 255,
            fontStyle: 'bold'
        },
        footStyles: {
            fillColor: [240, 240, 240],
            textColor: 0,
            fontStyle: 'bold'
        },
        styles: {
            fontSize: 8,
            cellPadding: 2,
            valign: 'middle'
        },
        columnStyles: {
            0: { cellWidth: 15, halign: 'center' },
            1: { cellWidth: 25 },
            2: { cellWidth: 25 },
            3: { cellWidth: 35 },
            4: { cellWidth: 15, halign: 'center' },
            5: { cellWidth: 25, halign: 'right' },
            6: { cellWidth: 25, halign: 'right' },
            7: { cellWidth: 20, halign: 'center' }
        }
    });

    // Download PDF
    const filename = fromDate === toDate 
        ? `Laporan_Glamping_Harian_${fromDate.replace(/-/g, '_')}.pdf`
        : `Laporan_Glamping_Harian_${fromDate.replace(/-/g, '_')}_to_${toDate.replace(/-/g, '_')}.pdf`;
    doc.save(filename);
}

function attachEventListeners() {
    const printButton = document.getElementById('printButton');
    if (printButton) {
        printButton.onclick = () => printElement('printArea');
    }
    
    const pdfButton = document.getElementById('pdfButton');
    if (pdfButton) {
        pdfButton.onclick = generateDailyPDF;
    }
}

// Replace buttons with both print and PDF options
document.querySelector('.col-md-6.text-right').innerHTML = `
    <div class="btn-group">
        <button id="printButton" class="btn btn-success mr-2">
            <i class="fas fa-print"></i> Cetak
        </button>
        <button id="pdfButton" class="btn btn-danger">
            <i class="fas fa-file-pdf"></i> Download PDF
        </button>
    </div>
`;

// Attach event listeners after DOM loads
document.addEventListener('DOMContentLoaded', attachEventListeners);
</script>

<!-- Modal Konfirmasi Hapus Transaksi Glamping -->
<div class="modal fade" id="confirmDeleteGlampingModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteGlampingLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteGlampingLabel">Konfirmasi Hapus Transaksi Glamping</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Tampilkan nomor struk yang akan dihapus -->
                <p>Nomor Struk: <strong id="modalKodeStrukGlamping"></strong></p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Peringatan: Transaksi yang dihapus tidak dapat dikembalikan!</p>
                <p>Masukkan password admin untuk menghapus transaksi glamping ini:</p>
                <input type="password" id="deletePasswordGlamping" class="form-control" placeholder="Masukkan password admin">
                <small class="text-danger" id="error-message-glamping" style="display: none;">Password salah!</small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteGlamping">
                    <i class="fas fa-trash"></i> Hapus Transaksi
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include MD5 library for password hashing -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

<script>
// Variabel untuk menyimpan URL hapus glamping
let deleteUrlGlamping = '';

// Fungsi untuk memunculkan modal konfirmasi dan menampilkan nomor struk glamping
function confirmDeleteGlamping(url, kodeStruk) {
    deleteUrlGlamping = url;
    document.getElementById('modalKodeStrukGlamping').textContent = kodeStruk;
    // Reset field password dan error message
    document.getElementById('deletePasswordGlamping').value = '';
    document.getElementById('error-message-glamping').style.display = 'none';
    $('#confirmDeleteGlampingModal').modal('show');
}

// Event listener pada tombol konfirmasi hapus glamping
document.getElementById('confirmDeleteGlamping').addEventListener('click', function() {
    let password = document.getElementById('deletePasswordGlamping').value;
    let hashedPassword = CryptoJS.MD5(password).toString();
    let correctHash = 'e10adc3949ba59abbe56e057f20f883e'; // MD5 hash untuk "123456"

    if (hashedPassword === correctHash) {
        window.location.href = deleteUrlGlamping;
    } else {
        document.getElementById('error-message-glamping').style.display = 'block';
    }
});
</script>