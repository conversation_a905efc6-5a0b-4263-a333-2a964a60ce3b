<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/dataTables.bootstrap4.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/pnotify.custom.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/css/datatables.min.css" />
<link type="text/css" rel="stylesheet" href="<?php echo base_url(); ?>assets/grocery_crud/themes/tablestrap4/extras/TableTools/media/css/TableTools.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.19.0/js/md5.min.js"></script>

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Laporan Playground Harian</h1>
                    <h2 id="outlet_name" style="display:none;"><?=$outlet['outlet_name'];?></h2>
                <!-- Flashdata untuk pesan error/sukses -->
<?php if($this->session->flashdata('error')): ?>
    <div class="alert alert-danger">
        <?php echo $this->session->flashdata('error'); ?>
    </div>
<?php endif; ?>

<?php if($this->session->flashdata('success')): ?>
    <div class="alert alert-success">
        <?php echo $this->session->flashdata('success'); ?>
    </div>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-9">
                                    <form class="form-inline" method="get">
                                        <div class="form-group mr-2">
                                            <label class="mr-2">Pilih Tanggal: </label>
                                            <input type="date" name="start_date" class="form-control" value="<?= $start_date ?>">
                                               <label class="mr-3 ml-3"> Sampai </label>
                                               <input type="date" name="end_date" class="form-control"  value="<?= $end_date ?>">
                                        </div>
                                        <button type="submit" class="btn btn-success">Tampilkan</button>
                                    </form>
                                </div>
                                <div class="col-md-3 text-right">
                                    <button onclick="window.print();" class="btn btn-success">
                                        <i class="fas fa-print"></i> Cetak
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive" id="printArea">
                                <table class="table table-bordered table-striped" id="tabel" >
                                    <thead>
                                        <tr>
                                             <th>Tanggal</th>
                                             <th>No Struk</th>
                                            <th>Tiket Playground</th>
                                            <th>Metode Bayar</th>
                                            <th>Total Harga</th>
                                        <?php if ($this->session->userdata('role') == 'admin') { ?>
                                            <th>Kasir</th>
                                            <th>Aksi</th>
                                        <?php } ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $total = 0; $total_tunai = 0; $total_qris = 0; $total_tiket = 0;
                                        foreach($laporan as $item): 
                                            $total += $item->total_playground;
                                            $total_tiket += $item->jml_item;
                                            if ($item->metode_bayar == 'tunai')
                                            $total_tunai += $item->total_playground;

                                            if ($item->metode_bayar == 'qris')
                                            $total_qris += $item->total_playground;

                                        ?>
                                        <tr>
                                             <td><?= $item->tanggal ?></td>
                                             <td><?= $item->kode_struk ?></td>
                                            <td><?= $item->jml_item ?></td>
                                            <td><?= ucfirst($item->metode_bayar) ?></td>
                                            <td>Rp <?= number_format($item->total_playground, 0, ',', '.') ?></td>
                                            <?php if ($this->session->userdata('role') == 'admin') { ?>
                                                <td><?= $item->kasir ?></td>
                                                <td>
    <button class="btn btn-primary btn-sm"
        onclick="window.open('<?=base_url();?>/order/cetak/<?= $item->kode_struk ?>', 'Cetak Struk', 'location=no,width=450,height=650,scrollbars=yes,top=100,left=700,resizable=no');">
        <i class="fas fa-eye"></i> Lihat Struk
    </button>
    <button class="btn btn-danger btn-sm"
        onclick="confirmDeletePlayground('<?=base_url();?>/laporan/hapus_trx_playground_harian/<?= $item->kode_struk ?>?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>', '<?= $item->kode_struk ?>');">
        <i class="fas fa-trash"></i> Hapus
    </button>
</td>
                                            <?php } ?>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="<?= $this->session->userdata('role') == 'admin' ? '6' : '4' ?>" class="text-right">Total Tiket Playground:</th>
                                            <th><?= $total_tiket ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="<?= $this->session->userdata('role') == 'admin' ? '6' : '4' ?>" class="text-right">Total Tunai:</th>
                                            <th>Rp <?= number_format($total_tunai, 0, ',', '.') ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="<?= $this->session->userdata('role') == 'admin' ? '6' : '4' ?>" class="text-right">Total Qris:</th>
                                            <th>Rp <?= number_format($total_qris, 0, ',', '.') ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="<?= $this->session->userdata('role') == 'admin' ? '6' : '4' ?>" class="text-right">Total Pendapatan Playground:</th>
                                            <th>Rp <?= number_format($total, 0, ',', '.') ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="<?php echo base_url(); ?>printer_assets/jspdf.umd.min.js"></script>
<script src="<?php echo base_url(); ?>printer_assets/jspdf.plugin.autotable.min.js"></script>
<script>

function decodeHTMLEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

function printElement(elementId) {
    const printContent = document.getElementById(elementId).innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="padding: 20px;">
            <h1 style="text-align: center; display: block !important;">${document.querySelector('h1.m-0').textContent}</h1>
            <h2 style="text-align: center; display: block !important;">${document.getElementById('outlet_name').textContent}</h2>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    
    // Reattach event listeners after restoring content
    attachEventListeners();
}

function generatePDF() {
    // Get the date range values
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;

    // Get outlet name from div
    const outletName = document.getElementById('outlet_name').textContent;

    // Format dates for display
    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

    // Initialize jsPDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Add main title
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.text('Laporan Playground Harian', doc.internal.pageSize.width / 2, 20, { align: 'center' });

    // Add outlet name as subtitle
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text(decodeHTMLEntities(outletName), doc.internal.pageSize.width / 2, 28, { align: 'center' });

    // Add date range
    if (startDate && endDate) {
        doc.setFontSize(11);
        doc.text(
            `Periode: ${formatDate(startDate)} sampai ${formatDate(endDate)}`,
            doc.internal.pageSize.width / 2,
            36,
            { align: 'center' }
        );
    }

    // Get data from table and decode HTML entities
    const tableData = [];
    let total = 0;
    
    document.querySelectorAll('table tbody tr').forEach(row => {
        const rowData = [
            decodeHTMLEntities(row.cells[0].textContent), // Tanggal
            decodeHTMLEntities(row.cells[1].textContent), // No Struk
            decodeHTMLEntities(row.cells[2].textContent), // Tiket Playground
            decodeHTMLEntities(row.cells[3].textContent), // Metode Bayar
            decodeHTMLEntities(row.cells[4].textContent)  // Total Harga
        ];
        tableData.push(rowData);
        
        // Calculate total (removing 'Rp ' and converting to number)
        const harga = parseInt(row.cells[4].textContent.replace('Rp ', '').replace(/\./g, ''));
        total += harga;
    });

    // Add table
    doc.autoTable({
        startY: 45,
        head: [['Tanggal', 'No Struk', 'Tiket Playground', 'Metode Bayar', 'Total Harga']],
        body: tableData,
        foot: [['', '', '', 'Total Pendapatan:', `Rp ${total.toLocaleString('id-ID')}`]],
        theme: 'grid',
        headStyles: {
            fillColor: [40, 167, 69],
            textColor: 255,
            fontStyle: 'bold'
        },
        footStyles: {
            fillColor: [240, 240, 240],
            textColor: 0,
            fontStyle: 'bold'
        },
        styles: {
            fontSize: 9,
            cellPadding: 2,
            valign: 'middle'
        },
        columnStyles: {
            0: { cellWidth: 35 },
            1: { cellWidth: 30 },
            2: { cellWidth: 25 },
            3: { cellWidth: 30 },
            4: { cellWidth: 35, halign: 'right' }
        }
    });

    // Download PDF
    const filename = `Laporan_Playground_${startDate || new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(filename);
}

function attachEventListeners() {
    const printButton = document.getElementById('printButton');
    if (printButton) {
        printButton.onclick = () => printElement('printArea');
    }
    
    const pdfButton = document.getElementById('pdfButton');
    if (pdfButton) {
        pdfButton.onclick = generatePDF;
    }
}

// Replace buttons with both print and PDF options
document.querySelector('.col-md-3.text-right').innerHTML = `
    <div class="btn-group">
        <button id="printButton" class="btn btn-success mr-2">
            <i class="fas fa-print"></i> Cetak
        </button>
        <button id="pdfButton" class="btn btn-danger">
            <i class="fas fa-file-pdf"></i> Download PDF
        </button>
    </div>
`;

    // Download PDF
    const filename = fromDate === toDate
        ? `Laporan_Playground_Harian_${fromDate.replace(/-/g, '_')}.pdf`
        : `Laporan_Playground_Harian_${fromDate.replace(/-/g, '_')}_to_${toDate.replace(/-/g, '_')}.pdf`;
    doc.save(filename);
}

function attachEventListeners() {
    const printButton = document.getElementById('printButton');
    if (printButton) {
        printButton.onclick = () => printElement('printArea');
    }

    const pdfButton = document.getElementById('pdfButton');
    if (pdfButton) {
        pdfButton.onclick = generateDailyPDF;
    }
}

// Attach event listeners when page loads
document.addEventListener('DOMContentLoaded', attachEventListeners);
</script>

<!-- Modal Konfirmasi Hapus Transaksi Playground -->
<div class="modal fade" id="confirmDeletePlaygroundModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeletePlaygroundLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeletePlaygroundLabel">Konfirmasi Hapus Transaksi Playground</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Tampilkan nomor struk yang akan dihapus -->
                <p>Nomor Struk: <strong id="modalKodeStrukPlayground"></strong></p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Peringatan: Transaksi yang dihapus tidak dapat dikembalikan!</p>
                <p>Masukkan password admin untuk menghapus transaksi playground ini:</p>
                <input type="password" id="deletePasswordPlayground" class="form-control" placeholder="Masukkan password admin">
                <small class="text-danger" id="error-message-playground" style="display: none;">Password salah!</small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePlayground">
                    <i class="fas fa-trash"></i> Hapus Transaksi
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include MD5 library for password hashing -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

<script>
// Variabel untuk menyimpan URL hapus playground
let deleteUrlPlayground = '';

// Fungsi untuk memunculkan modal konfirmasi dan menampilkan nomor struk playground
function confirmDeletePlayground(url, kodeStruk) {
    deleteUrlPlayground = url;
    document.getElementById('modalKodeStrukPlayground').textContent = kodeStruk;
    // Reset field password dan error message
    document.getElementById('deletePasswordPlayground').value = '';
    document.getElementById('error-message-playground').style.display = 'none';
    $('#confirmDeletePlaygroundModal').modal('show');
}

// Event listener pada tombol konfirmasi hapus playground
document.getElementById('confirmDeletePlayground').addEventListener('click', function() {
    let password = document.getElementById('deletePasswordPlayground').value;
    let hashedPassword = CryptoJS.MD5(password).toString();
    let correctHash = 'e10adc3949ba59abbe56e057f20f883e'; // MD5 hash untuk "123456"

    if (hashedPassword === correctHash) {
        window.location.href = deleteUrlPlayground;
    } else {
        document.getElementById('error-message-playground').style.display = 'block';
    }
});
</script>

// Attach event listeners after DOM loads
document.addEventListener('DOMContentLoaded', attachEventListeners);
</script>