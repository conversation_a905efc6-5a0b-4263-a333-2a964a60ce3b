
<?php
// application/views/footer.php
?>
    <footer class="main-footer">
        <div class="float-right d-none d-sm-block">
            <b>Version</b> 1.0.0
        </div>
        <strong> &copy; <?= date('Y') ?> <a href="<?= site_url() ?>">Glamping & playground Management System</a>.</strong> All rights reserved. Dev by : Rinocomp
    </footer>
</div>

<!-- jQuery -->
<script src="<?= base_url('assets/adminlte3/plugins/jquery/jquery.min.js') ?>"></script>
<!-- Bootstrap 4 -->
<script src="<?= base_url('assets/adminlte3/plugins/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>
<!-- AdminLTE App -->
<script src="<?= base_url('assets/adminlte3/dist/js/adminlte.min.js') ?>"></script>

<!-- Select2 -->
<script src="<?= base_url('');?>assets/adminlte3/plugins/select2/js/select2.full.min.js"></script>

<script src="<?= base_url('');?>assets/adminlte3/plugins/sweetalert2/sweetalert2.min.js"></script>






<?php if (isset($output->js_files)) foreach($output->js_files as $file): ?>
    <script src="<?= $file; ?>"></script>
<?php endforeach; ?>

<script>


$('.select2').select2({theme: 'bootstrap4',   width: '100%', });</script>
<!-- Grocery CRUD JS files -->
<!-- footer view custom -->

 <?php if  (isset($output_footer)) $this->load->view($output_footer); else echo  '<!-- kosong -->'; ?>

 <!-- footer view custom -->
<script>


    //active menu LTE
    var url = window.location.href;
    var pathname = window.location.pathname;

    // Fungsi untuk membandingkan URL tanpa parameter GET
    function compareUrls(linkHref, currentUrl) {
        // Ambil pathname dari link href
        var linkUrl = new URL(linkHref, window.location.origin);
        var currentUrlObj = new URL(currentUrl, window.location.origin);

        return linkUrl.pathname === currentUrlObj.pathname;
    }

    //MENU AKTIF - menggunakan pathname tanpa parameter GET
    $('ul.nav-sidebar a').filter(function () {
        var isMatch = compareUrls(this.href, url);
        if (isMatch){
            $('#icon-subjek').addClass(this.children[0].className);
        }
        return isMatch;
    }).addClass('active');

    //sidebar menu and treeview - menggunakan pathname tanpa parameter GET
    $('ul.nav-treeview a').filter(function () {
        return compareUrls(this.href, url);
    }).each(function() {
        // Tambahkan class active ke submenu yang cocok
        $(this).addClass('active');

        // Hanya buka parent menu jika submenu aktif (tanpa auto-expand semua)
        var $parentItem = $(this).closest('.nav-item.has-treeview');

        // Tambahkan class menu-open hanya ke parent yang memiliki submenu aktif
        $parentItem.addClass('menu-open');

        // Tambahkan class active ke parent link
        $parentItem.children('a.nav-link').addClass('active');

        // Pastikan submenu terlihat
        $parentItem.children('.nav-treeview').show();
    });

    // Custom JavaScript untuk mengontrol menu treeview behavior
    $(document).ready(function() {
        // Disable AdminLTE default treeview behavior
        $('[data-widget="treeview"]').off('click.lte.treeview');

        // Handle click pada menu parent yang memiliki submenu (hanya yang href="#")
        $('.nav-sidebar .nav-item.has-treeview > .nav-link[href="#"]').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $parentItem = $(this).parent('.nav-item.has-treeview');
            var $submenu = $parentItem.children('.nav-treeview');

            // Toggle menu yang diklik
            if ($parentItem.hasClass('menu-open')) {
                $parentItem.removeClass('menu-open');
                $submenu.slideUp(300);
            } else {
                // Jika accordion mode aktif, tutup menu lain
                if ($('.nav-sidebar').attr('data-accordion') !== 'false') {
                    $('.nav-sidebar .nav-item.has-treeview.menu-open').each(function() {
                        if (!$(this).is($parentItem)) {
                            $(this).removeClass('menu-open');
                            $(this).children('.nav-treeview').slideUp(300);
                        }
                    });
                }

                $parentItem.addClass('menu-open');
                $submenu.slideDown(300);
            }
        });

        // Pastikan submenu yang diklik bisa navigate (tidak di-prevent)
        $('.nav-sidebar .nav-treeview a.nav-link:not([href="#"])').on('click', function(e) {
            // Biarkan link submenu berfungsi normal
            // Tambahkan visual feedback
            $('.nav-sidebar .nav-treeview a.nav-link').removeClass('active');
            $(this).addClass('active');

            // Optional: Tambahkan loading indicator
            $(this).append(' <i class="fas fa-spinner fa-spin ml-2"></i>');
        });

        // Pastikan menu yang aktif tetap terbuka saat page load
        setTimeout(function() {
            $('.nav-sidebar .nav-treeview a.active').each(function() {
                var $parentItem = $(this).closest('.nav-item.has-treeview');
                $parentItem.addClass('menu-open');
                $parentItem.children('.nav-treeview').show();

                // Buka semua parent menu jika nested
                $(this).parents('.nav-item.has-treeview').each(function() {
                    $(this).addClass('menu-open');
                    $(this).children('.nav-treeview').show();
                });
            });
        }, 100);
    });

function printReport() {
    window.print();
}
function printElement_tes(elementId) {
    // Buat iframe sebagai wadah konten yang akan dicetak
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    
    // Ambil elemen yang akan dicetak
    const elementToPrint = document.getElementById(elementId);
    if (!elementToPrint) {
        console.error('Element tidak ditemukan');
        return;
    }

    // Salin elemen dan ubah display: none menjadi block
    const elementClone = elementToPrint.cloneNode(true);
    const hiddenElements = elementClone.querySelectorAll('[style*="display: none"]');
    hiddenElements.forEach(el => {
        el.style.display = 'block';
    });
    
    // Set konten iframe
    const iframeDoc = iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(`
        <html>
            <head>
                <style>
                    @media print {
                        body { margin: 0; padding: 15px; }
                        * { visibility: visible; }
                        [style*="display: none"] { display: block !important; }
                    }
                </style>
                <!-- Copy stylesheet dari parent document -->
                ${Array.from(document.styleSheets)
                    .map(styleSheet => {
                        try {
                            return styleSheet.href ? 
                                `<link rel="stylesheet" href="${styleSheet.href}">` : 
                                `<style>${Array.from(styleSheet.cssRules)
                                    .map(rule => rule.cssText)
                                    .join('\n')}</style>`;
                        } catch(e) {
                            return '';
                        }
                    }).join('\n')}
            </head>
            <body>
                ${elementClone.outerHTML}
            </body>
        </html>
    `);
    iframeDoc.close();

    // Tunggu semua resource loaded
    iframe.onload = function() {
        try {
            iframe.contentWindow.print();
            
            // Hapus iframe setelah print
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 1000);
        } catch(e) {
            console.error('Gagal mencetak:', e);
        }
    };
}

// Versi dengan custom styling
function printElementWithStyles(elementId, customStyles = '') {
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    
    const elementToPrint = document.getElementById(elementId);
    if (!elementToPrint) {
        console.error('Element tidak ditemukan');
        return;
    }

    const elementClone = elementToPrint.cloneNode(true);
    const hiddenElements = elementClone.querySelectorAll('[style*="display: none"]');
    hiddenElements.forEach(el => {
        el.style.display = 'block';
    });

    const iframeDoc = iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(`
        <html>
            <head>
                <style>
                    @media print {
                        body { margin: 0; padding: 15px; }
                        * { visibility: visible; }
                        [style*="display: none"] { display: block !important; }
                        ${customStyles}
                    }
                </style>
                ${Array.from(document.styleSheets)
                    .map(styleSheet => {
                        try {
                            return styleSheet.href ? 
                                `<link rel="stylesheet" href="${styleSheet.href}">` : 
                                `<style>${Array.from(styleSheet.cssRules)
                                    .map(rule => rule.cssText)
                                    .join('\n')}</style>`;
                        } catch(e) {
                            return '';
                        }
                    }).join('\n')}
            </head>
            <body>
                ${elementClone.outerHTML}
            </body>
        </html>
    `);
    iframeDoc.close();

    iframe.onload = function() {
        try {
            iframe.contentWindow.print();
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 1000);
        } catch(e) {
            console.error('Gagal mencetak:', e);
        }
    };
}

</script>



</body>
</html>