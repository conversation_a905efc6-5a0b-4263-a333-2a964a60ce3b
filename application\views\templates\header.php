<?php
// application/views/header.php
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= $page_title ?></title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="<?= base_url('assets/adminlte3/plugins/fontawesome-free/css/all.min.css') ?>">
    <!-- Theme style -->
    <link rel="stylesheet" href="<?= base_url('assets/adminlte3/dist/css/adminlte.min.css') ?>">


    <link rel="stylesheet" href="<?= base_url('assets/adminlte3/plugins/select2/css/select2.min.css') ?> ">
  <link rel="stylesheet" href="<?= base_url();?>assets/adminlte3/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">

 <link rel="stylesheet" href="<?= base_url();?>assets/adminlte3/plugins/sweetalert2/sweetalert2.min.css">
    <!-- Grocery CRUD CSS files -->
    <?php if (isset($output->css_files)) foreach($output->css_files as $file): ?>
        <link type="text/css" rel="stylesheet" href="<?= $file; ?>" />
    <?php endforeach; ?>
    <style type="text/css">
        .chosen-container {
            width: 100%;
        }

        /* Custom CSS untuk mengontrol sidebar menu behavior */
        .nav-sidebar .nav-treeview {
            display: none !important;
        }

        .nav-sidebar .nav-item.menu-open > .nav-treeview {
            display: block !important;
        }

        /* Pastikan submenu nested juga tersembunyi */
        .nav-sidebar .nav-treeview .nav-item.has-treeview .nav-treeview {
            display: none !important;
        }

        .nav-sidebar .nav-treeview .nav-item.has-treeview.menu-open .nav-treeview {
            display: block !important;
        }

        /* Styling untuk menu yang aktif */
        .nav-sidebar .nav-item.menu-open > .nav-link {
            background-color: rgba(255,255,255,.1);
        }

        /* Pastikan submenu bisa diklik */
        .nav-sidebar .nav-treeview a.nav-link {
            pointer-events: auto !important;
            cursor: pointer !important;
        }

        /* Hover effect untuk submenu */
        .nav-sidebar .nav-treeview a.nav-link:hover {
            background-color: rgba(255,255,255,.1);
        }
    </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav ml-auto">
         

            <li class="nav-item">
                <a class="nav-link"  role="button">
                    <i class="fas fa-user mr-2"></i>  <?=$this->session->userdata('username');?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?= site_url('auth/logout') ?>">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </li>
        </ul>
    </nav>
