# Fitur Hapus Transaksi - Playground & Glamping

## 🎯 **Overview**

Fitur hapus transaksi telah ditambahkan untuk laporan playground harian dan glamping harian dengan sistem keamanan berlapis:
- **Role-based Access**: <PERSON>ya admin yang dapat menghapus transaksi
- **Password Confirmation**: Memerlukan password admin untuk konfirmasi
- **Modal Confirmation**: Dialog konfirmasi sebelum penghapusan
- **Soft Delete**: Transaksi tidak dihapus permanen, hanya diubah status menjadi 'delete'

## 🔧 **Implementasi**

### **1. Controller Methods**

#### **File: `application/controllers/Laporan.php`**

```php
// Hapus Transaksi Playground Harian
public function hapus_trx_playground_harian($id)
{
    if ($this->session->userdata('role') === 'admin') {
        // Ambil filter tanggal dari GET
        $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : date('Y-m-d');
        $end_date   = $this->input->get('end_date') ? $this->input->get('end_date') : date('Y-m-d');
    
        // Proses penghapusan transaksi berdasarkan kode struk
        $this->db->where('kode_struk', trim($id));
        $this->db->update('struk_pembayaran', ['status' => 'delete']);
    
        // Set flashdata sukses
        $this->session->set_flashdata('success', 'Transaksi playground berhasil dihapus.');
    
        // Redirect kembali ke halaman laporan playground harian dengan parameter tanggal yang sama
        redirect('laporan/playground_harian?start_date=' . $start_date . '&end_date=' . $end_date);
    } else {
        // Jika bukan admin, redirect dengan pesan error
        $this->session->set_flashdata('error', 'Akses ditolak. Hanya admin yang dapat menghapus transaksi.');
        redirect('laporan/playground_harian');
    }
}

// Hapus Transaksi Glamping Harian
public function hapus_trx_glamping_harian($id)
{
    if ($this->session->userdata('role') === 'admin') {
        // Ambil filter tanggal dari GET
        $dari_tanggal = $this->input->get('dari_tanggal') ? $this->input->get('dari_tanggal') : date('Y-m-d');
        $sampai_tanggal = $this->input->get('sampai_tanggal') ? $this->input->get('sampai_tanggal') : date('Y-m-d');
    
        // Proses penghapusan transaksi berdasarkan kode struk
        $this->db->where('kode_struk', trim($id));
        $this->db->update('struk_pembayaran', ['status' => 'delete']);
    
        // Set flashdata sukses
        $this->session->set_flashdata('success', 'Transaksi glamping berhasil dihapus.');
    
        // Redirect kembali ke halaman laporan glamping harian dengan parameter tanggal yang sama
        redirect('laporan/glamping_harian?dari_tanggal=' . $dari_tanggal . '&sampai_tanggal=' . $sampai_tanggal);
    } else {
        // Jika bukan admin, redirect dengan pesan error
        $this->session->set_flashdata('error', 'Akses ditolak. Hanya admin yang dapat menghapus transaksi.');
        redirect('laporan/glamping_harian');
    }
}
```

### **2. View Updates**

#### **Playground Harian (`application/views/laporan/playground_harian.php`)**

**Tombol Aksi:**
```html
<?php if ($this->session->userdata('role') == 'admin') { ?>
<td>
    <button class="btn btn-primary btn-sm"
        onclick="window.open('<?=base_url();?>/order/cetak/<?= $item->kode_struk ?>', 'Cetak Struk', 'location=no,width=450,height=650,scrollbars=yes,top=100,left=700,resizable=no');">
        <i class="fas fa-eye"></i> Lihat Struk
    </button>
    <button class="btn btn-danger btn-sm"
        onclick="confirmDeletePlayground('<?=base_url();?>/laporan/hapus_trx_playground_harian/<?= $item->kode_struk ?>?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>', '<?= $item->kode_struk ?>');">
        <i class="fas fa-trash"></i> Hapus
    </button>
</td>
<?php } ?>
```

#### **Glamping Harian (`application/views/laporan/glamping_harian.php`)**

**Header Tabel:**
```html
<thead>
    <tr>
        <th>No</th>
        <th>Tanggal</th>
        <th>Kode Transaksi</th>
        <th>Nama Item</th>
        <th>Jumlah</th>
        <th>Harga Satuan</th>
        <th>Total</th>
        <th>Status</th>
        <?php if ($this->session->userdata('role') == 'admin') { ?>
        <th>Aksi</th>
        <?php } ?>
    </tr>
</thead>
```

**Tombol Aksi:**
```html
<?php if ($this->session->userdata('role') == 'admin') { ?>
<td>
    <button class="btn btn-primary btn-sm"
        onclick="window.open('<?=base_url();?>/order/cetak/<?= $item->kode_struk ?>', 'Cetak Struk', 'location=no,width=450,height=650,scrollbars=yes,top=100,left=700,resizable=no');">
        <i class="fas fa-eye"></i> Lihat Struk
    </button>
    <button class="btn btn-danger btn-sm"
        onclick="confirmDeleteGlamping('<?=base_url();?>/laporan/hapus_trx_glamping_harian/<?= $item->kode_struk ?>?dari_tanggal=<?= $dari_tanggal ?>&sampai_tanggal=<?= $sampai_tanggal ?>', '<?= $item->kode_struk ?>');">
        <i class="fas fa-trash"></i> Hapus
    </button>
</td>
<?php } ?>
```

### **3. Modal Konfirmasi**

#### **Modal Playground:**
```html
<!-- Modal Konfirmasi Hapus Transaksi Playground -->
<div class="modal fade" id="confirmDeletePlaygroundModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus Transaksi Playground</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Nomor Struk: <strong id="modalKodeStrukPlayground"></strong></p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Peringatan: Transaksi yang dihapus tidak dapat dikembalikan!</p>
                <p>Masukkan password admin untuk menghapus transaksi playground ini:</p>
                <input type="password" id="deletePasswordPlayground" class="form-control" placeholder="Masukkan password admin">
                <small class="text-danger" id="error-message-playground" style="display: none;">Password salah!</small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePlayground">
                    <i class="fas fa-trash"></i> Hapus Transaksi
                </button>
            </div>
        </div>
    </div>
</div>
```

### **4. JavaScript Functions**

#### **Password Validation:**
```javascript
// Event listener pada tombol konfirmasi hapus playground
document.getElementById('confirmDeletePlayground').addEventListener('click', function() {
    let password = document.getElementById('deletePasswordPlayground').value;
    let hashedPassword = CryptoJS.MD5(password).toString(); 
    let correctHash = 'e10adc3949ba59abbe56e057f20f883e'; // MD5 hash untuk "123456"
    
    if (hashedPassword === correctHash) {
        window.location.href = deleteUrlPlayground;
    } else {
        document.getElementById('error-message-playground').style.display = 'block';
    }
});
```

## 🔒 **Security Features**

### **1. Role-based Access Control**
- ✅ Hanya user dengan role 'admin' yang dapat melihat tombol hapus
- ✅ Controller method memvalidasi role sebelum eksekusi
- ✅ Redirect dengan error message jika akses ditolak

### **2. Password Confirmation**
- ✅ Modal konfirmasi meminta password admin
- ✅ Password di-hash menggunakan MD5 (CryptoJS)
- ✅ Validasi client-side sebelum submit
- ✅ Default password: "123456" (hash: e10adc3949ba59abbe56e057f20f883e)

### **3. Soft Delete**
- ✅ Transaksi tidak dihapus permanen dari database
- ✅ Status diubah menjadi 'delete' di tabel struk_pembayaran
- ✅ Data masih dapat dipulihkan jika diperlukan

### **4. User Experience**
- ✅ Modal konfirmasi dengan peringatan jelas
- ✅ Flash message untuk feedback sukses/error
- ✅ Redirect kembali ke halaman yang sama dengan filter yang sama
- ✅ Icon dan styling yang konsisten

## 🧪 **Testing Scenarios**

### **Test Case 1: Admin Access**
1. Login sebagai admin
2. Buka laporan playground/glamping harian
3. **Expected**: Kolom "Aksi" terlihat dengan tombol "Lihat Struk" dan "Hapus"

### **Test Case 2: Non-Admin Access**
1. Login sebagai kasir
2. Buka laporan playground/glamping harian
3. **Expected**: Kolom "Aksi" tidak terlihat

### **Test Case 3: Delete with Correct Password**
1. Login sebagai admin
2. Klik tombol "Hapus" pada transaksi
3. Masukkan password "123456"
4. Klik "Hapus Transaksi"
5. **Expected**: Transaksi terhapus, muncul pesan sukses

### **Test Case 4: Delete with Wrong Password**
1. Login sebagai admin
2. Klik tombol "Hapus" pada transaksi
3. Masukkan password salah
4. Klik "Hapus Transaksi"
5. **Expected**: Muncul pesan "Password salah!", transaksi tidak terhapus

### **Test Case 5: Direct URL Access (Non-Admin)**
1. Login sebagai kasir
2. Akses URL: `/laporan/hapus_trx_playground_harian/123`
3. **Expected**: Redirect dengan pesan error "Akses ditolak"

## 📝 **Notes**

### **Password Configuration**
- Default password: "123456"
- Untuk mengubah password, ganti hash MD5 di JavaScript
- Gunakan online MD5 generator untuk membuat hash baru

### **Database Impact**
- Tidak ada perubahan struktur database
- Menggunakan kolom 'status' yang sudah ada
- Transaksi dengan status 'delete' tidak muncul di laporan

### **Future Enhancements**
- Implementasi password admin yang dapat diubah dari settings
- Log audit untuk tracking penghapusan transaksi
- Fitur restore transaksi yang terhapus
- Bulk delete untuk multiple transaksi
