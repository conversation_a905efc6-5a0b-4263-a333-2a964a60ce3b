<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sidebar Menu</title>
    <style>
        .nav-sidebar .nav-treeview {
            display: none !important;
        }
        
        .nav-sidebar .nav-item.menu-open > .nav-treeview {
            display: block !important;
        }
        
        .nav-sidebar .nav-treeview .nav-item.has-treeview .nav-treeview {
            display: none !important;
        }
        
        .nav-sidebar .nav-treeview .nav-item.has-treeview.menu-open .nav-treeview {
            display: block !important;
        }
        
        .nav-sidebar .nav-item.menu-open > .nav-link {
            background-color: rgba(255,255,255,.1);
        }
        
        .nav-sidebar .nav-treeview a.nav-link {
            pointer-events: auto !important;
            cursor: pointer !important;
        }
        
        .nav-sidebar .nav-treeview a.nav-link:hover {
            background-color: rgba(255,255,255,.1);
        }
        
        /* Basic styling for test */
        .nav-sidebar {
            background: #343a40;
            color: white;
            width: 250px;
            padding: 10px;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
        }
        
        .nav-treeview {
            padding-left: 20px;
        }
        
        .nav-item {
            list-style: none;
        }
        
        ul {
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="nav-sidebar">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="true">
            <li class="nav-item">
                <a href="#dashboard" class="nav-link">Dashboard</a>
            </li>
            <li class="nav-item has-treeview">
                <a href="#" class="nav-link">
                    Laporan
                </a>
                <ul class="nav nav-treeview">
                    <li class="nav-item">
                        <a href="#laporan-bulanan" class="nav-link">Laporan Bulanan</a>
                    </li>
                    <li class="nav-item has-treeview">
                        <a href="#" class="nav-link">Laporan Playground</a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#playground-harian" class="nav-link">Playground Harian</a>
                            </li>
                            <li class="nav-item">
                                <a href="#playground-bulanan" class="nav-link">Playground Bulanan</a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item has-treeview">
                        <a href="#" class="nav-link">Laporan Glamping</a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#glamping-harian" class="nav-link">Glamping Harian</a>
                            </li>
                            <li class="nav-item">
                                <a href="#glamping-bulanan" class="nav-link">Glamping Bulanan</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </li>
        </ul>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Disable AdminLTE default treeview behavior
            $('[data-widget="treeview"]').off('click.lte.treeview');
            
            // Handle click pada menu parent yang memiliki submenu (hanya yang href="#")
            $('.nav-sidebar .nav-item.has-treeview > .nav-link[href="#"]').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('Parent menu clicked:', $(this).text().trim());
                
                var $parentItem = $(this).parent('.nav-item.has-treeview');
                var $submenu = $parentItem.children('.nav-treeview');
                
                // Toggle menu yang diklik
                if ($parentItem.hasClass('menu-open')) {
                    $parentItem.removeClass('menu-open');
                    $submenu.slideUp(300);
                    console.log('Menu closed');
                } else {
                    // Jika accordion mode aktif, tutup menu lain
                    if ($('.nav-sidebar').attr('data-accordion') !== 'false') {
                        $('.nav-sidebar .nav-item.has-treeview.menu-open').each(function() {
                            if (!$(this).is($parentItem)) {
                                $(this).removeClass('menu-open');
                                $(this).children('.nav-treeview').slideUp(300);
                            }
                        });
                    }
                    
                    $parentItem.addClass('menu-open');
                    $submenu.slideDown(300);
                    console.log('Menu opened');
                }
            });
            
            // Pastikan submenu yang diklik bisa navigate (tidak di-prevent)
            $('.nav-sidebar .nav-treeview a.nav-link:not([href="#"])').on('click', function(e) {
                console.log('Submenu clicked:', $(this).text().trim(), 'href:', $(this).attr('href'));
                
                // Biarkan link submenu berfungsi normal
                // Tambahkan visual feedback
                $('.nav-sidebar .nav-treeview a.nav-link').removeClass('active');
                $(this).addClass('active');
                
                // Simulate navigation (for test)
                alert('Navigating to: ' + $(this).text().trim());
            });
        });
    </script>
</body>
</html>
